{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["+vdhgq1A1k7znkT/bLW9zR36THT6rPxitgl/paaymrE=", "IFDQ6yNeZilubklrJqwZ3XT/O3c8tSvxfUyYk1gC610=", "9CFyNIxZvu8A5fzsfNvMCExQV0ofeq+ozyqktwYgZlU=", "uMw8DspB7isJuv3JrSbYx85xP1AWgtlle5gVpCQhnCc=", "sg9EC+1LeCB7u8DeysuHZp2dLpPaoiGavB+tTSUdOjg=", "kWtWoObgZMfUcGNamKx8XPJHxJEYD5miOtByywMk2w0=", "Hmcf2l0x+HrxAw/4udLSeQ1MGkqzJWM+u/2PWKQiF+E=", "dQrhoAmKzoLQmWzoZBi2tEKIZ5R4E7yPMy7hUl1XD0c=", "YfZai9ULoHkyWRHmQrq7dYuA8QYnZFY/238nchoyvvM=", "5r4Kc2tZzb7X+E2I/cQnIaD48isnrJqqh7dDGKVwRNg=", "OqP63DcofIqY99fwLdgh2eQ0qxiskSIeRzH6l6ejpuc=", "3zGYByOf4Emr3EdwrFaLRAS7j/ehvTkxGNa8YHremU0=", "HYt12/rOo7pDZxuXtzzcsHCjDVBUStA76X+aPID9VO0=", "r02DSCc0SfwyML+6VSUQQrUz7gn9v9M3qonNI/+BVAE=", "cqTRls6wUQCa8a0kZs4qT/i9EMcACSP9YVvkVDbY2Ys=", "Yx0l9pODdrrJlVCdWqkyaqSNmFrwQkQM2Q10CoaOdnI=", "9pQsGelkYg0DL7y+yyuNoDoxDXjCimIap1wh7ogSy0k=", "VtBB/CCTDZCGvi87x6u8Aa+gYeWyJnxJ1TsxuWVYvtw=", "35sJu24rm47iRh12NKGIoA7HrXsWr2tYbansiU81hF8=", "Z29uoGIO5uFTlJq/yrH+1Ia3sdzcQrEf87gw5PvNabc=", "z5G+JOJI3/gE7WBtVrmx2+ZpESgEpvGZS8CO36d5vGw=", "r0cRkpLr1hGqGwZL17itm3zbEnTgj16khdCKLeFZRbw=", "OxZ9QzJcNRDe+Dyxr6jVulkasDBJLUcydnLjGiEt2DY=", "nCou5PajbB1pIesg9l41njSzoagrJ7O3raONEobK9ns=", "/f+TuRwwXqAKMAHzbr5CPEoacPE6RbdC5KnjMr4wnWM=", "2HkPv/puuM8fHMLOUMljmHbEvZUqF2OdIFVcgrEKh+E=", "6InP+LIj21cYOsRWrjGsjjnASLTiW208zIgUvMMItOM=", "falAKc4eBqYZIfa4s1zgH5CI1RBKen7M7++ABK3UDkY=", "VAjPJIqT6qjYPbhOpg7o+4OMpqAkHO4b1D9J81XUOgg=", "mBjjqRbtpmQB7czx0wvsT3/pzIW/rCfu77iOo7eU/d4=", "O/mKVAl0f48jNhlyQGiE9N0FvBSWG7vhcpdBauU/Z18=", "SdjwlKckM/v+h7yy+ESJ9Dh+5mFFveFL6ffCC7W3R1w=", "ubYfMOj5JyvxYfGca5lLxI5lkozmi9Ra6DQ+YVrv7b8=", "yaxHaR3tKMIkxL3zTZ3dw2VffJEr5EqF+opjMx6tjj0=", "e5rjIUaKSqCOnOeSwvFhZ6HsApj+q/7FARv+CQXXoN0=", "+f4eS2whumQJgdgZDWY4fzgGxbzbXN1oX+N4VgAebC8=", "93TqL64KPLBAuQw1WO/dr+WNb7j8B4uWWFgJc9JBy1w=", "lVZWyWhTKnJZf1pOM2KxAzuoYy/ETsVVuzDf8phQKe4=", "rER/hEekQ+Dyb5ug+FQH7LTDNCGLDCKSn/bizEgAJjs=", "Q9CHB6H1ovfiRm2sRrNqg9+W+NkSZCLT6Q84yVrY+zI=", "krC3DEYmVii4Iz12bDkVQq0oG/bGu3xpvx/1mr4aK0s=", "1yOsT6zmiYYtsBVgv6jR8r3pLUX77HVDKXiqyEfTfcA=", "/AQWMPWJpUt21WUuj8d+koERUWezKjU/lp3fIEUs3Bg=", "5/UO/jq8jdriVLFEj0BK66Ev9wB/QWRiNLtMoK7odd0=", "XRILI/HauvOEb6IAtBTUG65vpy8+HErzZogdMxFnCt0=", "WTRKO8TcM8ywING6yRam4Z4UqpT56SHvoR0wgXhkvtY=", "GPDglG/O6Il29kVVQV21L6ZTFUhnHa1QzwrfGT+xEYY=", "q0HQSU13Pm4wh7VmOieX+LEMu2QsMlzH9WnHb5T7+gA=", "DXVetKBTAiQ7WdeatZGyzabJjKg5/453bupSfG8k8uc=", "mcxlBWUppEX64n4Sm36Q8mCEwimUvoBaYlCvKmwIKxo=", "lyRDNqdJ8bc+75J/NBIklgDhBeMeGovHOP+3Wnuj/rU=", "4MRomJD84m1ZU3uAW/8tfGfGsBCnBLriXgrW64UUesE=", "wyZCeGXLA4tmAbytVDFSaGB8NCeLGVWGWaT9lFtPXg4=", "fkWpMij4hhqzwrV62+gFw0AH/wNAagubStuBpXQJkwE=", "OB4Dq3Rm1Slk3k7P6aQ3uYIJ3EzAl79DYvJ7RoOttfA=", "pbYJGPRddRdOgCia0j5RDrBREdX/RWEs0UEO4emaNdI=", "UtVGNVxqHbakaJYX8fohKGPb6Jectddk53/5aZliaPc=", "rlQiAJhCRRcXW5CG/2gcVILmOXAQ/VPAuQGpSgP07vA=", "O55kzcwJkAaUTbX9AOw6n6L2kF8zG7XCSLLLOgQSLvQ=", "Q9kls7x7FaLfTtb6mY3r7JHomm7KeUaoqQM5BFznzi8=", "g/Ckrqi9Wv+ro8KGRE68pIxQEe6my3R5mLpdQFYe+iU=", "YklZV4dCBLlvgPPwGRFepPb2UJeuuBE3e5fh5+sVKHw=", "s9JjXpBDIBrz37b9K1Y5lTooTEArFdb67TwKo5ub8FM=", "gnJh16yf1Z/BlPPfznrX+7jC0RmJNxDj7Yj7ik0xtcg=", "I1isCGFAB3+nwq+NDtyUPoyA1lXZotwEdT4CNnBBj1Y=", "xDyy0b49rbfIkdtHhi0TKyb1Eq3pELUuAhpFxJnqKn8=", "5rf+Ao35fnCsNc3MmOVOKfFRHcch7CmGj1aI4DURWhk=", "7EW67joK1gbtoUAbeIclBDWSkdG0Rldb8F0EeqtUglc=", "Wi6fBQnxqeeMXzfyKy0YtijXoHB2MakRK9BX3porzbs=", "qpIIVmy8IlpNWs0x40fmwmjXwAyBmAPZEGYqQpx/2U8=", "p6ytRPP03FSI5S2MXJ6c/G0Ie5r2OJPCGXJxEXLDaTI=", "FyD133BPCIu8WQAlLl3K9LE4WbhevqCleyaPxsYbwok=", "WIK1vXUktOy2OcOex94bL1nJ2UI/wyQkKXgpwatGm2I=", "BXLDJujtOAEx7Rrvrpk2eZ3HuUKZEeTUQxd7lWnpydw=", "C36NEX6BpZ32aBbjDcLbmZA71x1pxAIPQ9rJvd5MSBc=", "Ugei/azcmZTvSCF3fZmNmk+dUEjWMIRah3evea/AldY=", "klt5MVRne4owO4VukKX8SpQmc2NmfKMRvS6D/SVUKOI=", "si8mgSHaCEX5+EmFvUASb0NxxNRpxxmHV6UyujEW9Hs=", "E3yB+wMbt+dcBOsqDSwgE+SOAhebdASg8QraYs1HAzU=", "K0tMOKyVgEEcEErZy1qyvpFyET8aIXVLcdrlyTaZDm8=", "XTVJ7FJE06M6QH//6d2YPpXHGAgd/A2BUVVoyHeNdYE=", "+zAzeqI7LeEpFQ7FyQU5KERm945bCRlghkzV6vSvILI=", "37i2YRJucsu4FDreswp6c/R0lKCx5Inm0hyud5VrT+k=", "jtQ4X9yglWUY1TmSriyzTeZSq4CVn+q9tRI8h0VSfRo=", "t3dXcy4jH9Lg6YD6SsK+lV3f9zxCgFMxsa/ZqLr8rlo=", "EARR5gLJiMdFm08iCICd5zHllSguZH95Hsqjqee1wZY=", "MxN9Oms3SnLnzUqq7zVE1Jb/qPRrzlJE9ioTHVAM0Co=", "pIJXIG+hV1/efDdfjc3voc0nxAHYNJuwk6FQnFXQbag=", "8+OzewJ7y4E0m/kc7JO11W38Mt6vpbyVRNjG2GYWfrU=", "gAqTybs5jFD7PU+bFHk0IF9gtKcjAphk/KQGK2oZMWA=", "Ye50TPK/vdb6SRLJrjDwD1ikBHdiAho1zXPkFwZgR/w=", "CusR0tX8PMELRifv/WKJ2HQ5As7nQzWXCsosL0TyzSU=", "jVYTX6J4cqTTwGBzxnjrSjcbd2h4hTf47SEVt+gz+CM=", "M60WEebxe2wk8+jEOuQuby7qvS3txREx7l+VkM/clwo=", "Y20RRaOfKKSQghuDReUgz5DJtu1F9RjTSK3UJGAl9iA=", "6d4dZqb+r9C/u9o6OJS9GiRLSwzgzR4EUjR/dxPN6jo=", "fWGE4bYIRDGPJfhjPUtlQdkrueqrlJfVLd89pPoah40=", "/xQtZBTB2M2a+rJ1byz+s3rHo/z/0/iB49BtETqnL+E=", "sIuyQpAozsfW2az43NhX3M0mBuD2cNuz/tcis36jKoI=", "qULfw6BupmRPe9aLIkUmQAOUPo1R7OwglFRlrfor2Sk=", "sgteY0m2R0DaZctKJOxbU03CDonsTHAwvNsGYkS+BJ4=", "yqWYcsKi5RQJ+2xrg3CRL2aDtHbujpfCqWVTJHJ7P60=", "i/VQLgs/LsYUVnBhN8sqRLXeplBHQvXW35eLFPgXAjg=", "zgGlnz8NuGnHgebToX5LE3npxD9cvqLkMos6/sGryTI=", "LM/fMs/fMGMdy/zWToNImj/0RyeCbOaXDrVZ/O5bkCI=", "SWo9WOR8hzukrh5q6bSqKodtal6BsEfE2HnQNMJwylQ=", "ybJgNCEdnTxCosytAC39pnwkXMZxKVxS+7RaJNL1Cho=", "V/1tnsAO/u2NKlUqdar+/CBv6E5iHcF4ignfDt3i/Pg=", "/CX4DSZoy//DyNYww6AyJbVlatkhU7TtTDmMqgbvqyc=", "WPlYQmMP9Ryaeurv3kNsdPwEStrguC7azYYfGvlyn3s=", "X8r89RH7qWeRQ4XPgntTXbYw3lpVdz2+zwLFcN66oiU=", "XmzEUmUdhe4PpWnaBtGtBGhkLEra0PlDkL+OVlQq7tw=", "FbIkZwfRYGLwBxuLI8YwcYKmpZw8lojnpgcvFprdLGs=", "4TMz8GyriqGspewVPN045XKBcTmRYSzUgTDsqJdeVb4=", "XPrvEGfi1pObyrtcK/JDJCkNJyy2a/S5RmgrWmptwQA=", "iK2/c+waXE/TCNY0p6AAODEPKwmmdFlajVoWuJnFCpI=", "8YRxsrBGqrr6tUf97EVPtqBwKE4r2mFGjwpLUJByQiE=", "AIITG/8Ix/Ma+VwmGKYY42YuQ4h8HgVMIQlrl9f2/WY=", "y8Bbe1mMJu2eVL8DGFe5lZwvfdoZ0w+TAxbY++wUsys=", "cNXAvG/FshN0iY4XuVS2hvlhmRMBrxWeba9ZKmy8E6o=", "oFYbaD3aWZqjnyRxgxNAN6Rs1Vn/p53VXtO8YxEnIbY=", "3AckH9GdQEK1VQN++UTD11QKGoJE5rhEAkDD40a5ouk=", "AVERtro0EH6q/oJOTsyKE7Wy5tWaf08oj9Vss+n8k+M=", "y1V01cEwhQ3H+jEcZl/UK8o7iEX0cpInkNcYpYZ0PYA=", "P1iwCp/1bbrLl/RySKKGphCkK2j9bAHjfVuBdJCUmWM=", "4bB2c/YZ/34noda1DvXKUqyT9R2e+IMkCPFjdwNIqh8=", "zCIrhkfNcVXbJGMkvaaR5CgGxyihp3i5iZrHjhILJQk=", "wZqSO719MEHXZPL0VLUUqYlEj03OHaTk7LJKh+mK0yw=", "EZBtBl4QUePQQcCGjN3subuqxbMF+Itg3ZAkkCERiL8=", "fKUTtIoJjZR/wd7HeZX7gqVtcss2D/AW9EG22kNtU28=", "gH5hsxDsZMUnCTTeDFZ2YcIv+IMNwkbvGdEkqtQUqw0=", "lgmNhja8H+XWUeV7OILg7L3dY8Uv5nvMSbtWoWb9hdE=", "Vlayu8AXE5RxrN9i25lcQHsIPoLFqfW51ysvAbFBMyY=", "j3xwFicd6uHfJR848tbZonObJBG/gYEMa0usvauXtRg=", "YsbSW5XcYg2Es1hX2YRjOEvI0QHckyNUA/DEwTfbMY4=", "65V4+HbuVXTjKnCidaXotdM7rntXNVCET3GjFNArP1I=", "yZFZ0cGJt+pDhbX5fYnKpU5q5wCoVCgMO5YEF1KExak=", "6twuAh16ZxSXbeORw0PqcxGu7xuvUOye/m38DHL6/vM=", "jrQ4+nRvYo516TmfsbLu6+R2EkPljqUyCH62PkysfgQ=", "lP4EwMaJTZqIdU6ftnIeiJPzHVv26FQFSe8L+i3wbY0=", "q0DLO17QUSllbuHHY9MTtK0vjigcdirDs38AyC5gCiM=", "ffykwCvGgRso6JcfVGQDG1YHTgitgK0kb226mor/uRk=", "TX8PVzce63c/cXNY5R8obZyHZvYPHY9ptw5cXi3OESA="], "CachedAssets": {"+vdhgq1A1k7znkT/bLW9zR36THT6rPxitgl/paaymrE=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\ofa40bqe71-b9sayid5wm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "css/site.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0suozzrugb", "Integrity": "0FC1RbVkLkwKhJ1oi+r2Hw4Tm9av6DyJeo95YVvfA3Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\css\\site.css", "FileLength": 318, "LastWriteTime": "2025-06-03T02:28:42.0601109+00:00"}, "IFDQ6yNeZilubklrJqwZ3XT/O3c8tSvxfUyYk1gC610=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\j9swiz3yzq-90yqlj465b.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "favicon.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fxo9vb9gbf", "Integrity": "cwHpDnBTabxDzOHXFPOawb7ZlL3eysr4s1HfAD/K3vA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\favicon.ico", "FileLength": 9541, "LastWriteTime": "2025-06-03T02:28:41.9978514+00:00"}, "9CFyNIxZvu8A5fzsfNvMCExQV0ofeq+ozyqktwYgZlU=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\vltxs1u3vm-xtxxf3hu2r.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "js/site.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rl5dcbfpcw", "Integrity": "Ydq7YvQUzhbo68waLZeQvZnPOvmTeQ+HyDqjkA1whsA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\js\\site.js", "FileLength": 189, "LastWriteTime": "2025-06-03T02:28:41.9958529+00:00"}, "uMw8DspB7isJuv3JrSbYx85xP1AWgtlle5gVpCQhnCc=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\14ipv7q33s-bqjiyaj88i.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-06-03T02:28:42.0003822+00:00"}, "sg9EC+1LeCB7u8DeysuHZp2dLpPaoiGavB+tTSUdOjg=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\rh65p086id-c2jlpeoesf.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-06-03T02:28:42.0169178+00:00"}, "kWtWoObgZMfUcGNamKx8XPJHxJEYD5miOtByywMk2w0=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\eb27mqwgz2-erw9l3u2r3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-06-03T02:28:42.0334904+00:00"}, "Hmcf2l0x+HrxAw/4udLSeQ1MGkqzJWM+u/2PWKQiF+E=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\9glsckmvxo-aexeepp0ev.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-06-03T02:28:42.0169178+00:00"}, "dQrhoAmKzoLQmWzoZBi2tEKIZ5R4E7yPMy7hUl1XD0c=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\hh8ihyobdx-d7shbmvgxk.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-06-03T02:28:42.037521+00:00"}, "YfZai9ULoHkyWRHmQrq7dYuA8QYnZFY/238nchoyvvM=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\bww8ud00yd-ausgxo2sd3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-06-03T02:28:42.0179659+00:00"}, "5r4Kc2tZzb7X+E2I/cQnIaD48isnrJqqh7dDGKVwRNg=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\edcrak57d7-k8d9w2qqmf.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-06-03T02:28:42.0319581+00:00"}, "OqP63DcofIqY99fwLdgh2eQ0qxiskSIeRzH6l6ejpuc=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\sf8kf2lula-cosvhxvwiu.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-06-03T02:28:42.0365214+00:00"}, "3zGYByOf4Emr3EdwrFaLRAS7j/ehvTkxGNa8YHremU0=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\3tzsqhslk9-ub07r2b239.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-06-03T02:28:42.0485151+00:00"}, "HYt12/rOo7pDZxuXtzzcsHCjDVBUStA76X+aPID9VO0=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\oxk5o6czdw-fvhpjtyr6v.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-06-03T02:28:42.0475155+00:00"}, "r02DSCc0SfwyML+6VSUQQrUz7gn9v9M3qonNI/+BVAE=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\k8b25g0zwc-b7pk76d08c.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-06-03T02:28:42.0511155+00:00"}, "cqTRls6wUQCa8a0kZs4qT/i9EMcACSP9YVvkVDbY2Ys=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\8axix7wldr-fsbi9cje9m.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-06-03T02:28:42.0601109+00:00"}, "Yx0l9pODdrrJlVCdWqkyaqSNmFrwQkQM2Q10CoaOdnI=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\d5z9sfpxbi-rzd6atqjts.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-06-03T02:28:42.0721118+00:00"}, "9pQsGelkYg0DL7y+yyuNoDoxDXjCimIap1wh7ogSy0k=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\u8428c4e9i-ee0r1s7dh0.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-06-03T02:28:42.0435174+00:00"}, "VtBB/CCTDZCGvi87x6u8Aa+gYeWyJnxJ1TsxuWVYvtw=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\tg53r17ghy-dxx9fxp4il.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-06-03T02:28:41.9978514+00:00"}, "35sJu24rm47iRh12NKGIoA7HrXsWr2tYbansiU81hF8=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\nlrl0f3xs3-jd9uben2k1.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-06-03T02:28:41.9988508+00:00"}, "Z29uoGIO5uFTlJq/yrH+1Ia3sdzcQrEf87gw5PvNabc=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\1fc4q00scq-khv3u5hwcm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-06-03T02:28:41.9988508+00:00"}, "z5G+JOJI3/gE7WBtVrmx2+ZpESgEpvGZS8CO36d5vGw=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\8xykfy6qmd-r4e9w2rdcm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-06-03T02:28:42.006412+00:00"}, "r0cRkpLr1hGqGwZL17itm3zbEnTgj16khdCKLeFZRbw=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\vsrbd71spn-lcd1t2u6c8.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-06-03T02:28:42.037521+00:00"}, "OxZ9QzJcNRDe+Dyxr6jVulkasDBJLUcydnLjGiEt2DY=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\cynp17w084-c2oey78nd0.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-06-03T02:28:42.0169178+00:00"}, "nCou5PajbB1pIesg9l41njSzoagrJ7O3raONEobK9ns=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\7o8oyvng68-tdbxkamptv.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-06-03T02:28:42.0329577+00:00"}, "/f+TuRwwXqAKMAHzbr5CPEoacPE6RbdC5KnjMr4wnWM=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\h9vwtoirpy-j5mq2jizvt.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-06-03T02:28:42.0229628+00:00"}, "2HkPv/puuM8fHMLOUMljmHbEvZUqF2OdIFVcgrEKh+E=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\o5k23vqhrk-06098lyss8.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-06-03T02:28:42.0345223+00:00"}, "6InP+LIj21cYOsRWrjGsjjnASLTiW208zIgUvMMItOM=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\iznc766avz-nvvlpmu67g.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-06-03T02:28:42.0415185+00:00"}, "falAKc4eBqYZIfa4s1zgH5CI1RBKen7M7++ABK3UDkY=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\ns4583i71s-s35ty4nyc5.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-06-03T02:28:42.0611099+00:00"}, "VAjPJIqT6qjYPbhOpg7o+4OMpqAkHO4b1D9J81XUOgg=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\47012z8l2l-pj5nd1wqec.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-06-03T02:28:42.0741099+00:00"}, "mBjjqRbtpmQB7czx0wvsT3/pzIW/rCfu77iOo7eU/d4=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\4vzefxwp2m-46ein0sx1k.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-06-03T02:28:42.079107+00:00"}, "O/mKVAl0f48jNhlyQGiE9N0FvBSWG7vhcpdBauU/Z18=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\bh4v3mdph9-v0zj4ognzu.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-06-03T02:28:42.0511155+00:00"}, "SdjwlKckM/v+h7yy+ESJ9Dh+5mFFveFL6ffCC7W3R1w=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\l186vlgaag-37tfw0ft22.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-06-03T02:28:42.0701126+00:00"}, "ubYfMOj5JyvxYfGca5lLxI5lkozmi9Ra6DQ+YVrv7b8=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\rsc7qacj1u-hrwsygsryq.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-06-03T02:28:42.0886618+00:00"}, "yaxHaR3tKMIkxL3zTZ3dw2VffJEr5EqF+opjMx6tjj0=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\z408qb6q7a-pk9g2wxc8p.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-06-03T02:28:42.0936593+00:00"}, "e5rjIUaKSqCOnOeSwvFhZ6HsApj+q/7FARv+CQXXoN0=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\qfr28sqcy1-ft3s53vfgj.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-06-03T02:28:42.008411+00:00"}, "+f4eS2whumQJgdgZDWY4fzgGxbzbXN1oX+N4VgAebC8=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\0k1i85ntyh-6cfz1n2cew.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sh9sbpd29q", "Integrity": "8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44354, "LastWriteTime": "2025-06-03T02:28:42.0199646+00:00"}, "93TqL64KPLBAuQw1WO/dr+WNb7j8B4uWWFgJc9JBy1w=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\xa379ftczi-6pdc2jztkx.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgz3bfuucz", "Integrity": "tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-06-03T02:28:42.0299594+00:00"}, "lVZWyWhTKnJZf1pOM2KxAzuoYy/ETsVVuzDf8phQKe4=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\lr5hmrr0j7-493y06b0oq.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-06-03T02:28:42.0124087+00:00"}, "rER/hEekQ+Dyb5ug+FQH7LTDNCGLDCKSn/bizEgAJjs=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\ivy2s9gwh5-iovd86k7lj.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svb68clhd0", "Integrity": "WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-06-03T02:28:42.0385203+00:00"}, "Q9CHB6H1ovfiRm2sRrNqg9+W+NkSZCLT6Q84yVrY+zI=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\higufxz8op-vr1egmr9el.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jtj7j0yqni", "Integrity": "6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28852, "LastWriteTime": "2025-06-03T02:28:42.0435174+00:00"}, "krC3DEYmVii4Iz12bDkVQq0oG/bGu3xpvx/1mr4aK0s=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\s3tglgz8hr-kbrnm935zg.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmaqe8uvdz", "Integrity": "UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-06-03T02:28:42.0701126+00:00"}, "1yOsT6zmiYYtsBVgv6jR8r3pLUX77HVDKXiqyEfTfcA=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\vw9sls9bhj-jj8uyg4cgr.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-06-03T02:28:42.0395196+00:00"}, "/AQWMPWJpUt21WUuj8d+koERUWezKjU/lp3fIEUs3Bg=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\ym8tkpcl2i-y7v9cxd14o.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klzs96wner", "Integrity": "OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-06-03T02:28:42.079107+00:00"}, "5/UO/jq8jdriVLFEj0BK66Ev9wB/QWRiNLtMoK7odd0=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\zzna4nrm5w-notf2xhcfb.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4d5rpgxe6z", "Integrity": "6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29569, "LastWriteTime": "2025-06-03T02:28:42.0435174+00:00"}, "XRILI/HauvOEb6IAtBTUG65vpy8+HErzZogdMxFnCt0=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\igscs4x0yk-h1s4sie4z3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42umladzh7", "Integrity": "rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-06-03T02:28:42.0601109+00:00"}, "WTRKO8TcM8ywING6yRam4Z4UqpT56SHvoR0wgXhkvtY=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\zap00c0tb5-63fj8s7r0e.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-06-03T02:28:42.0731108+00:00"}, "GPDglG/O6Il29kVVQV21L6ZTFUhnHa1QzwrfGT+xEYY=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\pogzkicjpz-0j3bgjxly4.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n46fiwynw7", "Integrity": "OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-06-03T02:28:42.0866629+00:00"}, "q0HQSU13Pm4wh7VmOieX+LEMu2QsMlzH9WnHb5T7+gA=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\j3hwsq15kh-47otxtyo56.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7mrxpa<PERSON><PERSON><PERSON>", "Integrity": "8WSd7G1SSJrTChp1H0sIwU3oPQXDsKXVF1KkXEF6LuI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 4651, "LastWriteTime": "2025-06-03T02:28:42.0500799+00:00"}, "DXVetKBTAiQ7WdeatZGyzabJjKg5/453bupSfG8k8uc=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\q3naettu8c-4v8eqarkd7.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kw5m9o3hck", "Integrity": "ZECRGyeVrglmY0fxP9wVujqjjSoecu3G7roYlvaINus=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 2207, "LastWriteTime": "2025-06-03T02:28:42.0601109+00:00"}, "mcxlBWUppEX64n4Sm36Q8mCEwimUvoBaYlCvKmwIKxo=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\ldxy2n3w6q-356vix0kms.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yhmugv22u", "Integrity": "WpL0kbwDHONKwUu+fAC6nKQXtZOBY20Gsspcn336Iz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 694, "LastWriteTime": "2025-06-03T02:28:42.0701126+00:00"}, "lyRDNqdJ8bc+75J/NBIklgDhBeMeGovHOP+3Wnuj/rU=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\ypthv7q62w-83jwlth58m.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/additional-methods.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8j9rvpuvlg", "Integrity": "BIw6qCD4ysNHqPUgCkXJ/h+XlQ/h+2MuYEbvs/ZN+kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 14078, "LastWriteTime": "2025-06-03T02:28:42.0731108+00:00"}, "4MRomJD84m1ZU3uAW/8tfGfGsBCnBLriXgrW64UUesE=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\t6e3b82hrf-mrlpezrjn3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/additional-methods.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uya4g6am6w", "Integrity": "hhhjkgaWyJk2NWrF/LyhTIDOF42U+kBX72IU2K4RWJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 6482, "LastWriteTime": "2025-06-03T02:28:41.9938509+00:00"}, "wyZCeGXLA4tmAbytVDFSaGB8NCeLGVWGWaT9lFtPXg4=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\uocis9wxbx-lzl9nlhx6b.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/jquery.validate.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "anlp8nykfw", "Integrity": "KuJPTvhuArDs4mj2zziNxM+hBWFbOhi0BKQgeR1pHG8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 14068, "LastWriteTime": "2025-06-03T02:28:41.9988508+00:00"}, "fkWpMij4hhqzwrV62+gFw0AH/wNAagubStuBpXQJkwE=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\2tikzqlmtu-ag7o75518u.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hgmochx07c", "Integrity": "T/jNUyeLYdKQ3zndN8zs507jIg6GnkENMxuBqc1eXs0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 8121, "LastWriteTime": "2025-06-03T02:28:41.9978514+00:00"}, "OB4Dq3Rm1Slk3k7P6aQ3uYIJ3EzAl79DYvJ7RoOttfA=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\9muusbdg0a-x0q3zqp4vz.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/LICENSE.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1fp9w2itex", "Integrity": "7bAe2yT3obcUnzDyFQyuh4GI4iC/NGB4Bs4gZaY3Wj4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\LICENSE.md", "FileLength": 683, "LastWriteTime": "2025-06-03T02:28:42.0034136+00:00"}, "pbYJGPRddRdOgCia0j5RDrBREdX/RWEs0UEO4emaNdI=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\tpsq5dibur-0i3buxo5is.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "slhp14mdwv", "Integrity": "YPYGuVoBNkA8pP/JjlyjSON9Q7Ej1gVEUkbHrQT3uJU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.js", "FileLength": 84431, "LastWriteTime": "2025-06-03T02:28:42.0229628+00:00"}, "UtVGNVxqHbakaJYX8fohKGPb6Jectddk53/5aZliaPc=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\t0qo7o574p-o1o13a6vjx.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4efnowp72v", "Integrity": "rJUWNDEom3xIYh3mH5HCPpyh/CGhP0YhaSsaX6IOiDk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 30683, "LastWriteTime": "2025-06-03T02:28:42.015407+00:00"}, "rlQiAJhCRRcXW5CG/2gcVILmOXAQ/VPAuQGpSgP07vA=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\b9lhb08218-ttgo8qnofa.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.min.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hq2hiirxit", "Integrity": "GfL7M76amKU+B+H1o4WVh7q13GpiC7L6scp3ODOz3ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 54456, "LastWriteTime": "2025-06-03T02:28:42.0455167+00:00"}, "O55kzcwJkAaUTbX9AOw6n6L2kF8zG7XCSLLLOgQSLvQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\a3ygkwa5zy-2z0ns9nrw6.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xlmiz42j7j", "Integrity": "9bWYPof03vxzc0YRmjwb6berDC7SXtE8rlvXxhIlakE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 68601, "LastWriteTime": "2025-06-03T02:28:42.0239629+00:00"}, "Q9kls7x7FaLfTtb6mY3r7JHomm7KeUaoqQM5BFznzi8=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\nezrqnk58p-muycvpuwrr.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "trtvcfsjjx", "Integrity": "h68a4ojlMeHZ2MJNcpHGvBgERdoUw7Qpxk6a/oPD5kQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 24360, "LastWriteTime": "2025-06-03T02:28:42.0435174+00:00"}, "g/Ckrqi9Wv+ro8KGRE68pIxQEe6my3R5mLpdQFYe+iU=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\v0zgfp0y55-87fc7y1x7t.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.min.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p5ev1mo4sb", "Integrity": "bZLUSM9fRhNJKF4yFjKvv9iU/j2aC1lJtEqR9u5n6y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 43123, "LastWriteTime": "2025-06-03T02:28:42.0365214+00:00"}, "YklZV4dCBLlvgPPwGRFepPb2UJeuuBE3e5fh5+sVKHw=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\yfc7ypekbg-mlv21k5csn.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/LICENSE.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k2cjzt041l", "Integrity": "JKWeHUaANNuffeRatdc54UCb84RoxjEw/nTq8Mops8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\LICENSE.txt", "FileLength": 682, "LastWriteTime": "2025-06-03T02:28:42.0425175+00:00"}, "s9JjXpBDIBrz37b9K1Y5lTooTEArFdb67TwKo5ub8FM=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\8ngr7uwcar-zyh9mpbhuf.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "css/admin#[.{fingerprint=zyh9mpbhuf}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\css\\admin.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "u08tgmy2tt", "Integrity": "3pXWWlIVWcYM6gqWci0TTA6SlU9Iu+vWT4tdLgXH5OA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\css\\admin.css", "FileLength": 7594, "LastWriteTime": "2025-06-03T02:28:42.0511155+00:00"}, "gnJh16yf1Z/BlPPfznrX+7jC0RmJNxDj7Yj7ik0xtcg=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\vzqep69h03-ttlnujb3md.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "css/chatbot#[.{fingerprint=ttlnujb3md}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\css\\chatbot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "i3oc660vng", "Integrity": "Ap0MJU654qvh5mJKl0/CazXUcGLSMDuvMETk+sIdhVo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\css\\chatbot.css", "FileLength": 2529, "LastWriteTime": "2025-06-03T02:28:42.0591115+00:00"}, "I1isCGFAB3+nwq+NDtyUPoyA1lXZotwEdT4CNnBBj1Y=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\pn3zjfqylx-g07bnx142d.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "css/client#[.{fingerprint=g07bnx142d}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\css\\client.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "u372l94rk3", "Integrity": "hnZ3ayaGZFLAG6FjKMU6gjkmJLNka/tR9IAAQRpSPoc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\css\\client.css", "FileLength": 4346, "LastWriteTime": "2025-06-03T02:28:42.0500799+00:00"}, "xDyy0b49rbfIkdtHhi0TKyb1Eq3pELUuAhpFxJnqKn8=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\elvx5emtjm-wilnnc3w1m.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "css/custom#[.{fingerprint=wilnnc3w1m}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\css\\custom.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k58f1us78f", "Integrity": "CDABZ8Got8gWH2Wic8T6MC1izVu156P7c25rWmLiqLI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\css\\custom.css", "FileLength": 212, "LastWriteTime": "2025-06-03T02:28:42.0581115+00:00"}, "5rf+Ao35fnCsNc3MmOVOKfFRHcch7CmGj1aI4DURWhk=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\ypbfssc6px-1edbnb0gu6.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "css/hero-slideshow#[.{fingerprint=1edbnb0gu6}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\css\\hero-slideshow.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kg1721j3l3", "Integrity": "3CYDHawx/sbpzjIQoN7+wnjWSSXtjNUj7txCQ3kydJI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\css\\hero-slideshow.css", "FileLength": 1718, "LastWriteTime": "2025-06-03T02:28:42.0611099+00:00"}, "7EW67joK1gbtoUAbeIclBDWSkdG0Rldb8F0EeqtUglc=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\xtt04yhxc6-0q4my8jvfl.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "css/modern-homepage#[.{fingerprint=0q4my8jvfl}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\css\\modern-homepage.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rcvxhwxgzm", "Integrity": "BaKfeGlWkAeD/OI5bepfDPACZjrHdgAWyxNU1bDp3LE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\css\\modern-homepage.css", "FileLength": 14185, "LastWriteTime": "2025-06-03T02:28:42.0701126+00:00"}, "Wi6fBQnxqeeMXzfyKy0YtijXoHB2MakRK9BX3porzbs=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\7ejlrje5jg-8znpj9knio.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "css/site#[.{fingerprint=8znpj9knio}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tb45argtbf", "Integrity": "KXsoPJcXUP9jdH3J4w7jvn/bvOC4Ia8KvN2KNFAyres=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\css\\site.css", "FileLength": 2281, "LastWriteTime": "2025-06-03T02:28:41.9938509+00:00"}, "qpIIVmy8IlpNWs0x40fmwmjXwAyBmAPZEGYqQpx/2U8=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\03l1rte6gq-61n19gt1b8.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "favicon#[.{fingerprint=61n19gt1b8}]?.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nezsohjtde", "Integrity": "wfFuuYm+Lh6RbCeeiUqxw334b/hIOkp5j9eokGUM1Y0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\favicon.ico", "FileLength": 2468, "LastWriteTime": "2025-06-03T02:28:41.9938509+00:00"}, "p6ytRPP03FSI5S2MXJ6c/G0Ie5r2OJPCGXJxEXLDaTI=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\ktm00ucnju-wgrwvlfr5s.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/technologies/delphi#[.{fingerprint=wgrwvlfr5s}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\images\\technologies\\delphi.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tkbigiuy64", "Integrity": "HQM1ysqWHmUnuAlp3aK0g+H3KTdRrGOY3H0Vq22/RWA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\images\\technologies\\delphi.svg", "FileLength": 1935, "LastWriteTime": "2025-06-03T02:28:41.9948536+00:00"}, "FyD133BPCIu8WQAlLl3K9LE4WbhevqCleyaPxsYbwok=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\qveyjng0ys-nedvdbx254.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/technologies/haskell#[.{fingerprint=nedvdbx254}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\images\\technologies\\haskell.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "edsoh1itdo", "Integrity": "olpJlaPKyLgPjxsEUdPEwIwMpvYmE5KFaI68j+OJmJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\images\\technologies\\haskell.svg", "FileLength": 336, "LastWriteTime": "2025-06-03T02:28:42.0003822+00:00"}, "WIK1vXUktOy2OcOex94bL1nJ2UI/wyQkKXgpwatGm2I=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\ikbcgss9f7-k8cq9l2kib.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/technologies/nodejs#[.{fingerprint=k8cq9l2kib}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\images\\technologies\\nodejs.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lpoc1bcrbp", "Integrity": "GTXsRzccKflDEkzr6cT5Iq+5KyyN46VgzOgbjaIisWo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\images\\technologies\\nodejs.svg", "FileLength": 721, "LastWriteTime": "2025-06-03T02:28:42.006412+00:00"}, "BXLDJujtOAEx7Rrvrpk2eZ3HuUKZEeTUQxd7lWnpydw=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\nnketcjwya-1x8wi64s97.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/technologies/python#[.{fingerprint=1x8wi64s97}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\images\\technologies\\python.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j6yyh4b7nm", "Integrity": "i5K/+SeOT5dWe0FWBa0GIm1JLWXKxGhZVNxsYkLDrns=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\images\\technologies\\python.svg", "FileLength": 734, "LastWriteTime": "2025-06-03T02:28:41.9998501+00:00"}, "C36NEX6BpZ32aBbjDcLbmZA71x1pxAIPQ9rJvd5MSBc=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\uham8ign5j-emsbkume29.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/technologies/swift#[.{fingerprint=emsbkume29}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\images\\technologies\\swift.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rvaavaxrke", "Integrity": "afO1CiL+LYXrmSV0hG/nzRY58iy/trDc+maeMmc1q8Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\images\\technologies\\swift.svg", "FileLength": 619, "LastWriteTime": "2025-06-03T02:28:42.005413+00:00"}, "Ugei/azcmZTvSCF3fZmNmk+dUEjWMIRah3evea/AldY=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\10owx4zk9i-y36mr8d3re.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/technoloway-logo#[.{fingerprint=y36mr8d3re}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\images\\technoloway-logo.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "78ry7ld12l", "Integrity": "qVZsciNX4bvJyG/NFAFG6/1bWRdPLgFZLpfMSEqLyZo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\images\\technoloway-logo.svg", "FileLength": 110221, "LastWriteTime": "2025-06-03T02:28:42.0219635+00:00"}, "klt5MVRne4owO4VukKX8SpQmc2NmfKMRvS6D/SVUKOI=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\02bln8vcid-6de5fjlu8q.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "js/admin#[.{fingerprint=6de5fjlu8q}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\js\\admin.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bkj3u4c828", "Integrity": "y7OG3+AqATtB8KCnImHlz9DaMCW/SvkgUDW/ww3awkw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\js\\admin.js", "FileLength": 5688, "LastWriteTime": "2025-06-03T02:28:42.0309584+00:00"}, "si8mgSHaCEX5+EmFvUASb0NxxNRpxxmHV6UyujEW9Hs=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\wb4hq6364b-ulg6rxb1b8.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "js/chatbot#[.{fingerprint=ulg6rxb1b8}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\js\\chatbot.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "17ine8nxd9", "Integrity": "3Vn+bM4vca8/ukjw9Mqrls1a+eAN6e822dXlFn01kzI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\js\\chatbot.js", "FileLength": 4496, "LastWriteTime": "2025-06-03T02:28:42.0094102+00:00"}, "E3yB+wMbt+dcBOsqDSwgE+SOAhebdASg8QraYs1HAzU=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\v8u4sxh3fr-3xu3gfddrx.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "js/client#[.{fingerprint=3xu3gfddrx}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\js\\client.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kkcwqz3pwi", "Integrity": "pD2v2NXmu8dIQdlAvDxSQC28LtmTLNKzC22UlumYprU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\js\\client.js", "FileLength": 390, "LastWriteTime": "2025-06-03T02:28:42.0114097+00:00"}, "K0tMOKyVgEEcEErZy1qyvpFyET8aIXVLcdrlyTaZDm8=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\h4d7zuu3lf-rd81zyuzir.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "js/hero-slideshow#[.{fingerprint=rd81zyuzir}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\js\\hero-slideshow.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6lq72lcfcg", "Integrity": "pkfBPs6ownzjPebnXD0fcxTJYLLrd3SJgTGnCXXCxXo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\js\\hero-slideshow.js", "FileLength": 1678, "LastWriteTime": "2025-06-03T02:28:42.0114097+00:00"}, "XTVJ7FJE06M6QH//6d2YPpXHGAgd/A2BUVVoyHeNdYE=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\br1wthuj05-wi7ss39h6q.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "js/homepage-animations#[.{fingerprint=wi7ss39h6q}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\js\\homepage-animations.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iftq384gbj", "Integrity": "gqWjPa9AQ/+yPWfZCbXLhtYUgjFs8Oxn+qn1YXdxUNA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\js\\homepage-animations.js", "FileLength": 2761, "LastWriteTime": "2025-06-03T02:28:42.0134082+00:00"}, "+zAzeqI7LeEpFQ7FyQU5KERm945bCRlghkzV6vSvILI=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\5eaaj8jli5-2svxw61t86.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "js/site#[.{fingerprint=2svxw61t86}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "o75gd3adie", "Integrity": "ZHcCB5qku88L0iD5sq5euDqrhPm9N+4MeMk/tRrrgIg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\js\\site.js", "FileLength": 769, "LastWriteTime": "2025-06-03T02:28:42.0094102+00:00"}, "37i2YRJucsu4FDreswp6c/R0lKCx5Inm0hyud5VrT+k=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\huncoxf4nc-bqjiyaj88i.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint=bqjiyaj88i}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-06-03T02:28:42.0114097+00:00"}, "jtQ4X9yglWUY1TmSriyzTeZSq4CVn+q9tRI8h0VSfRo=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\2jnr46nmpc-c2jlpeoesf.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint=c2jlpeoesf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-06-03T02:28:42.020964+00:00"}, "t3dXcy4jH9Lg6YD6SsK+lV3f9zxCgFMxsa/ZqLr8rlo=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\wmzyo7xuhb-erw9l3u2r3.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint=erw9l3u2r3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-06-03T02:28:42.0345223+00:00"}, "EARR5gLJiMdFm08iCICd5zHllSguZH95Hsqjqee1wZY=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\djyxqsu5pv-aexeepp0ev.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint=aexeepp0ev}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-06-03T02:28:41.9988508+00:00"}, "MxN9Oms3SnLnzUqq7zVE1Jb/qPRrzlJE9ioTHVAM0Co=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\2sbuxwf498-d7shbmvgxk.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint=d7shbmvgxk}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-06-03T02:28:41.9978514+00:00"}, "pIJXIG+hV1/efDdfjc3voc0nxAHYNJuwk6FQnFXQbag=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\fe676pkbci-ausgxo2sd3.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint=ausgxo2sd3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-06-03T02:28:42.0269616+00:00"}, "8+OzewJ7y4E0m/kc7JO11W38Mt6vpbyVRNjG2GYWfrU=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\2n44t9y1oe-k8d9w2qqmf.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint=k8d9w2qqmf}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-06-03T02:28:42.005413+00:00"}, "gAqTybs5jFD7PU+bFHk0IF9gtKcjAphk/KQGK2oZMWA=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\2yiqndbjob-cosvhxvwiu.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint=cosvhxvwiu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-06-03T02:28:42.008411+00:00"}, "Ye50TPK/vdb6SRLJrjDwD1ikBHdiAho1zXPkFwZgR/w=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\6laymexpp1-ub07r2b239.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint=ub07r2b239}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-06-03T02:28:42.0044132+00:00"}, "CusR0tX8PMELRifv/WKJ2HQ5As7nQzWXCsosL0TyzSU=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\e23n28kbl6-fvhpjtyr6v.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint=fvhpjtyr6v}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-06-03T02:28:42.0124087+00:00"}, "jVYTX6J4cqTTwGBzxnjrSjcbd2h4hTf47SEVt+gz+CM=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\0xdmrg7j3x-b7pk76d08c.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint=b7pk76d08c}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-06-03T02:28:42.0500799+00:00"}, "M60WEebxe2wk8+jEOuQuby7qvS3txREx7l+VkM/clwo=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\7msow90u6m-fsbi9cje9m.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint=fsbi9cje9m}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-06-03T02:28:42.0661116+00:00"}, "Y20RRaOfKKSQghuDReUgz5DJtu1F9RjTSK3UJGAl9iA=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\y6lzeo23io-rzd6atqjts.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint=rzd6atqjts}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-06-03T02:28:42.087662+00:00"}, "6d4dZqb+r9C/u9o6OJS9GiRLSwzgzR4EUjR/dxPN6jo=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\b1eq4g63qz-ee0r1s7dh0.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint=ee0r1s7dh0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-06-03T02:28:42.0916601+00:00"}, "fWGE4bYIRDGPJfhjPUtlQdkrueqrlJfVLd89pPoah40=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\q7xywl88yg-dxx9fxp4il.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint=dxx9fxp4il}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-06-03T02:28:42.0299594+00:00"}, "/xQtZBTB2M2a+rJ1byz+s3rHo/z/0/iB49BtETqnL+E=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\tl4yfr4qca-jd9uben2k1.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint=jd9uben2k1}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-06-03T02:28:42.0365214+00:00"}, "sIuyQpAozsfW2az43NhX3M0mBuD2cNuz/tcis36jKoI=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\8xb1bhl2rn-khv3u5hwcm.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint=khv3u5hwcm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-06-03T02:28:42.0425175+00:00"}, "qULfw6BupmRPe9aLIkUmQAOUPo1R7OwglFRlrfor2Sk=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\1ju4p3k34w-r4e9w2rdcm.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint=r4e9w2rdcm}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-06-03T02:28:42.0581115+00:00"}, "sgteY0m2R0DaZctKJOxbU03CDonsTHAwvNsGYkS+BJ4=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\jyl4075mxi-lcd1t2u6c8.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint=lcd1t2u6c8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-06-03T02:28:42.0309584+00:00"}, "yqWYcsKi5RQJ+2xrg3CRL2aDtHbujpfCqWVTJHJ7P60=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\zul1z3i66m-c2oey78nd0.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint=c2oey78nd0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-06-03T02:28:42.0475155+00:00"}, "i/VQLgs/LsYUVnBhN8sqRLXeplBHQvXW35eLFPgXAjg=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\tj8j7uiu1e-tdbxkamptv.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint=tdbxkamptv}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-06-03T02:28:41.9978514+00:00"}, "zgGlnz8NuGnHgebToX5LE3npxD9cvqLkMos6/sGryTI=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\l6ou9js1o2-j5mq2jizvt.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint=j5mq2jizvt}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-06-03T02:28:42.0199646+00:00"}, "LM/fMs/fMGMdy/zWToNImj/0RyeCbOaXDrVZ/O5bkCI=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\z3s3pqtpk8-06098lyss8.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint=06098lyss8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-06-03T02:28:41.9988508+00:00"}, "SWo9WOR8hzukrh5q6bSqKodtal6BsEfE2HnQNMJwylQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\pkmk90gfad-nvvlpmu67g.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint=nvvlpmu67g}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-06-03T02:28:42.020964+00:00"}, "ybJgNCEdnTxCosytAC39pnwkXMZxKVxS+7RaJNL1Cho=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\zktux6y5y4-s35ty4nyc5.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint=s35ty4nyc5}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-06-03T02:28:42.0435174+00:00"}, "V/1tnsAO/u2NKlUqdar+/CBv6E5iHcF4ignfDt3i/Pg=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\26o4et715b-pj5nd1wqec.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint=pj5nd1wqec}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-06-03T02:28:42.0581115+00:00"}, "/CX4DSZoy//DyNYww6AyJbVlatkhU7TtTDmMqgbvqyc=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\7gp52xdync-46ein0sx1k.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint=46ein0sx1k}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-06-03T02:28:42.0741099+00:00"}, "WPlYQmMP9Ryaeurv3kNsdPwEStrguC7azYYfGvlyn3s=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\e7b6u3x55w-v0zj4ognzu.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint=v0zj4ognzu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-06-03T02:28:42.0751095+00:00"}, "X8r89RH7qWeRQ4XPgntTXbYw3lpVdz2+zwLFcN66oiU=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\9zg9c6chb7-37tfw0ft22.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint=37tfw0ft22}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-06-03T02:28:42.0866629+00:00"}, "XmzEUmUdhe4PpWnaBtGtBGhkLEra0PlDkL+OVlQq7tw=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\33czbugvzg-hrwsygsryq.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint=hrwsygsryq}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-06-03T02:28:42.0771083+00:00"}, "FbIkZwfRYGLwBxuLI8YwcYKmpZw8lojnpgcvFprdLGs=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\2d5epgijjo-pk9g2wxc8p.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint=pk9g2wxc8p}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-06-03T02:28:42.0856637+00:00"}, "4TMz8GyriqGspewVPN045XKBcTmRYSzUgTDsqJdeVb4=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\t63suxyd54-ft3s53vfgj.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint=ft3s53vfgj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-06-03T02:28:42.0988704+00:00"}, "XPrvEGfi1pObyrtcK/JDJCkNJyy2a/S5RmgrWmptwQA=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\w5r96tufml-6cfz1n2cew.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint=6cfz1n2cew}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sh9sbpd29q", "Integrity": "8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44354, "LastWriteTime": "2025-06-03T02:28:42.1047545+00:00"}, "iK2/c+waXE/TCNY0p6AAODEPKwmmdFlajVoWuJnFCpI=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\wegvxza2ge-6pdc2jztkx.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint=6pdc2jztkx}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgz3bfuucz", "Integrity": "tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-06-03T02:28:42.0731108+00:00"}, "8YRxsrBGqrr6tUf97EVPtqBwKE4r2mFGjwpLUJByQiE=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\g09mkto0sq-493y06b0oq.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint=493y06b0oq}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-06-03T02:28:42.0781076+00:00"}, "AIITG/8Ix/Ma+VwmGKYY42YuQ4h8HgVMIQlrl9f2/WY=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\4mg52w1lo3-iovd86k7lj.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint=iovd86k7lj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svb68clhd0", "Integrity": "WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-06-03T02:28:42.0886618+00:00"}, "y8Bbe1mMJu2eVL8DGFe5lZwvfdoZ0w+TAxbY++wUsys=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\lh2isbcsad-vr1egmr9el.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint=vr1egmr9el}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jtj7j0yqni", "Integrity": "6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28852, "LastWriteTime": "2025-06-03T02:28:42.0936593+00:00"}, "cNXAvG/FshN0iY4XuVS2hvlhmRMBrxWeba9ZKmy8E6o=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\g1ftby5py3-kbrnm935zg.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint=kbrnm935zg}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmaqe8uvdz", "Integrity": "UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-06-03T02:28:42.0189652+00:00"}, "oFYbaD3aWZqjnyRxgxNAN6Rs1Vn/p53VXtO8YxEnIbY=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\vphg83v0d2-jj8uyg4cgr.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint=jj8uyg4cgr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-06-03T02:28:42.0003822+00:00"}, "3AckH9GdQEK1VQN++UTD11QKGoJE5rhEAkDD40a5ouk=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\hd7xpsd4po-y7v9cxd14o.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint=y7v9cxd14o}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klzs96wner", "Integrity": "OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-06-03T02:28:42.0179659+00:00"}, "AVERtro0EH6q/oJOTsyKE7Wy5tWaf08oj9Vss+n8k+M=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\2626qsfhg1-notf2xhcfb.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint=notf2xhcfb}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4d5rpgxe6z", "Integrity": "6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29569, "LastWriteTime": "2025-06-03T02:28:42.0104097+00:00"}, "y1V01cEwhQ3H+jEcZl/UK8o7iEX0cpInkNcYpYZ0PYA=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\u8c68735sf-h1s4sie4z3.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint=h1s4sie4z3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42umladzh7", "Integrity": "rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-06-03T02:28:42.0199646+00:00"}, "P1iwCp/1bbrLl/RySKKGphCkK2j9bAHjfVuBdJCUmWM=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\fij7bwzp8x-63fj8s7r0e.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint=63fj8s7r0e}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-06-03T02:28:42.0334904+00:00"}, "4bB2c/YZ/34noda1DvXKUqyT9R2e+IMkCPFjdwNIqh8=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\cji4aywtqv-0j3bgjxly4.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint=0j3bgjxly4}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n46fiwynw7", "Integrity": "OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-06-03T02:28:42.0521148+00:00"}, "zCIrhkfNcVXbJGMkvaaR5CgGxyihp3i5iZrHjhILJQk=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\9mvculopuj-47otxtyo56.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint=47otxtyo56}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7mrxpa<PERSON><PERSON><PERSON>", "Integrity": "8WSd7G1SSJrTChp1H0sIwU3oPQXDsKXVF1KkXEF6LuI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 4651, "LastWriteTime": "2025-06-03T02:28:42.0365214+00:00"}, "wZqSO719MEHXZPL0VLUUqYlEj03OHaTk7LJKh+mK0yw=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\bf5okwooxo-4v8eqarkd7.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint=4v8eqarkd7}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kw5m9o3hck", "Integrity": "ZECRGyeVrglmY0fxP9wVujqjjSoecu3G7roYlvaINus=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 2207, "LastWriteTime": "2025-06-03T02:28:42.0500799+00:00"}, "EZBtBl4QUePQQcCGjN3subuqxbMF+Itg3ZAkkCERiL8=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\g10371glbs-356vix0kms.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint=356vix0kms}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yhmugv22u", "Integrity": "WpL0kbwDHONKwUu+fAC6nKQXtZOBY20Gsspcn336Iz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 694, "LastWriteTime": "2025-06-03T02:28:42.0531148+00:00"}, "fKUTtIoJjZR/wd7HeZX7gqVtcss2D/AW9EG22kNtU28=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\2iu5gkm6iv-83jwlth58m.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint=83jwlth58m}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8j9rvpuvlg", "Integrity": "BIw6qCD4ysNHqPUgCkXJ/h+XlQ/h+2MuYEbvs/ZN+kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 14078, "LastWriteTime": "2025-06-03T02:28:42.0701126+00:00"}, "gH5hsxDsZMUnCTTeDFZ2YcIv+IMNwkbvGdEkqtQUqw0=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\ahn74umtfx-mrlpezrjn3.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint=mrlpezrjn3}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uya4g6am6w", "Integrity": "hhhjkgaWyJk2NWrF/LyhTIDOF42U+kBX72IU2K4RWJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 6482, "LastWriteTime": "2025-06-03T02:28:42.0329577+00:00"}, "lgmNhja8H+XWUeV7OILg7L3dY8Uv5nvMSbtWoWb9hdE=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\8k9meis7w4-lzl9nlhx6b.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint=lzl9nlhx6b}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "anlp8nykfw", "Integrity": "KuJPTvhuArDs4mj2zziNxM+hBWFbOhi0BKQgeR1pHG8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 14068, "LastWriteTime": "2025-06-03T02:28:42.0425175+00:00"}, "Vlayu8AXE5RxrN9i25lcQHsIPoLFqfW51ysvAbFBMyY=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\6jty019ari-ag7o75518u.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint=ag7o75518u}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hgmochx07c", "Integrity": "T/jNUyeLYdKQ3zndN8zs507jIg6GnkENMxuBqc1eXs0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 8121, "LastWriteTime": "2025-06-03T02:28:42.0652307+00:00"}, "j3xwFicd6uHfJR848tbZonObJBG/gYEMa0usvauXtRg=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\w5qk1pgd3r-x0q3zqp4vz.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint=x0q3zqp4vz}]?.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1fp9w2itex", "Integrity": "7bAe2yT3obcUnzDyFQyuh4GI4iC/NGB4Bs4gZaY3Wj4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 683, "LastWriteTime": "2025-06-03T02:28:42.079107+00:00"}, "YsbSW5XcYg2Es1hX2YRjOEvI0QHckyNUA/DEwTfbMY4=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\64rjhqzavh-0i3buxo5is.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint=0i3buxo5is}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "slhp14mdwv", "Integrity": "YPYGuVoBNkA8pP/JjlyjSON9Q7Ej1gVEUkbHrQT3uJU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 84431, "LastWriteTime": "2025-06-03T02:28:42.0691128+00:00"}, "65V4+HbuVXTjKnCidaXotdM7rntXNVCET3GjFNArP1I=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\dmzx9f6yhi-o1o13a6vjx.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=o1o13a6vjx}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4efnowp72v", "Integrity": "rJUWNDEom3xIYh3mH5HCPpyh/CGhP0YhaSsaX6IOiDk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 30683, "LastWriteTime": "2025-06-03T02:28:42.0741099+00:00"}, "yZFZ0cGJt+pDhbX5fYnKpU5q5wCoVCgMO5YEF1KExak=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\zydbwp8g7y-ttgo8qnofa.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=ttgo8qnofa}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hq2hiirxit", "Integrity": "GfL7M76amKU+B+H1o4WVh7q13GpiC7L6scp3ODOz3ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 54456, "LastWriteTime": "2025-06-03T02:28:42.0239629+00:00"}, "6twuAh16ZxSXbeORw0PqcxGu7xuvUOye/m38DHL6/vM=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\tivk7iw1cd-2z0ns9nrw6.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint=2z0ns9nrw6}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xlmiz42j7j", "Integrity": "9bWYPof03vxzc0YRmjwb6berDC7SXtE8rlvXxhIlakE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 68601, "LastWriteTime": "2025-06-03T02:28:42.0134082+00:00"}, "jrQ4+nRvYo516TmfsbLu6+R2EkPljqUyCH62PkysfgQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\2v4zxome5j-muycvpuwrr.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=muycvpuwrr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "trtvcfsjjx", "Integrity": "h68a4ojlMeHZ2MJNcpHGvBgERdoUw7Qpxk6a/oPD5kQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 24360, "LastWriteTime": "2025-06-03T02:28:42.0024145+00:00"}, "lP4EwMaJTZqIdU6ftnIeiJPzHVv26FQFSe8L+i3wbY0=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\e15jphxp1u-87fc7y1x7t.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=87fc7y1x7t}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p5ev1mo4sb", "Integrity": "bZLUSM9fRhNJKF4yFjKvv9iU/j2aC1lJtEqR9u5n6y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 43123, "LastWriteTime": "2025-06-03T02:28:42.0299594+00:00"}, "q0DLO17QUSllbuHHY9MTtK0vjigcdirDs38AyC5gCiM=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\8ezk5r3swy-mlv21k5csn.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint=mlv21k5csn}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k2cjzt041l", "Integrity": "JKWeHUaANNuffeRatdc54UCb84RoxjEw/nTq8Mops8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 682, "LastWriteTime": "2025-06-03T02:28:42.0355216+00:00"}, "ffykwCvGgRso6JcfVGQDG1YHTgitgK0kb226mor/uRk=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\ot0u7hstz0-btisj24con.gz", "SourceId": "Technoloway.Web", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "Technoloway.Web#[.{fingerprint=btisj24con}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\scopedcss\\bundle\\Technoloway.Web.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f0ib5o3ag5", "Integrity": "72RHN3eTubIZVjEZactNL1gtgMjQIrVfDuIfhwQDTvk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\scopedcss\\bundle\\Technoloway.Web.styles.css", "FileLength": 545, "LastWriteTime": "2025-06-03T02:28:42.0601109+00:00"}, "TX8PVzce63c/cXNY5R8obZyHZvYPHY9ptw5cXi3OESA=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\on9a315cdm-btisj24con.gz", "SourceId": "Technoloway.Web", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "Technoloway.Web#[.{fingerprint=btisj24con}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Technoloway.Web.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f0ib5o3ag5", "Integrity": "72RHN3eTubIZVjEZactNL1gtgMjQIrVfDuIfhwQDTvk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Technoloway.Web.bundle.scp.css", "FileLength": 545, "LastWriteTime": "2025-06-03T02:28:42.0701126+00:00"}}, "CachedCopyCandidates": {}}